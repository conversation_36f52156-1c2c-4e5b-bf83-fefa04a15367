# -*- coding: utf-8 -*-
# =============================================================================
# Traditional Signal Tracker - Enhanced Win/Loss Tracking for Technical Signals
# AI-powered evaluation and optimization system
# =============================================================================

import sqlite3
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import requests

class TraditionalSignalTracker:
    """
    Enhanced tracking system for traditional technical analysis signals.
    Tracks win/loss patterns, analyzes causes, and optimizes signal generation.
    """
    
    def __init__(self, db_path: str = "traditional_signals.db"):
        self.db_path = db_path
        self.tracking_active = False
        self.tracking_thread = None
        self.check_interval = 30  # Check every 30 seconds
        self.init_database()
        
    def init_database(self):
        """Initialize database for traditional signal tracking."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Traditional signals table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS traditional_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    signal_id TEXT UNIQUE NOT NULL,
                    timestamp DATETIME NOT NULL,
                    asset TEXT NOT NULL,
                    signal_type TEXT NOT NULL,  -- Strong Buy, Strong Sell, etc.
                    score REAL NOT NULL,
                    entry_price REAL NOT NULL,
                    entry_time DATETIME NOT NULL,
                    expiry_time DATETIME NOT NULL,
                    expiry_minutes INTEGER NOT NULL,
                    timeframe TEXT NOT NULL,
                    indicators_data TEXT,  -- JSON of all indicators
                    market_conditions TEXT,  -- JSON of market state
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
                """)
                
                # Traditional signal results table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS traditional_signal_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    signal_id TEXT NOT NULL,
                    exit_price REAL NOT NULL,
                    result TEXT NOT NULL,  -- WIN, LOSS, TIE
                    profit_loss REAL NOT NULL,
                    evaluation_method TEXT NOT NULL,
                    evaluated_at DATETIME NOT NULL,
                    price_movement REAL,  -- Actual price change
                    volatility REAL,  -- Market volatility during signal
                    FOREIGN KEY (signal_id) REFERENCES traditional_signals (signal_id)
                )
                """)
                
                # Signal pattern analysis table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS signal_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern_name TEXT NOT NULL,
                    asset TEXT NOT NULL,
                    signal_type TEXT NOT NULL,
                    win_rate REAL NOT NULL,
                    total_signals INTEGER NOT NULL,
                    avg_score REAL NOT NULL,
                    best_timeframe TEXT,
                    best_conditions TEXT,  -- JSON
                    last_updated DATETIME NOT NULL
                )
                """)
                
                # AI learning insights table
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS ai_insights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    insight_type TEXT NOT NULL,  -- WIN_PATTERN, LOSS_PATTERN, OPTIMIZATION
                    asset TEXT,
                    signal_type TEXT,
                    insight_data TEXT NOT NULL,  -- JSON
                    confidence REAL NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    applied BOOLEAN DEFAULT FALSE
                )
                """)
                
                conn.commit()
                print("✅ Traditional signal tracking database initialized")
                
        except Exception as e:
            print(f"❌ Error initializing traditional signal database: {e}")
    
    def track_traditional_signal(self, signal_data: Dict[str, Any]) -> bool:
        """Track a traditional technical analysis signal."""
        try:
            signal_id = f"TRAD_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{signal_data.get('asset', 'UNK')}"
            
            # Calculate expiry time based on signal strength
            score = abs(signal_data.get('score', 0))
            if score >= 20:
                expiry_minutes = 15
            elif score >= 10:
                expiry_minutes = 10
            else:
                expiry_minutes = 5
                
            entry_time = datetime.now()
            expiry_time = entry_time + timedelta(minutes=expiry_minutes)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                INSERT INTO traditional_signals (
                    signal_id, timestamp, asset, signal_type, score,
                    entry_price, entry_time, expiry_time, expiry_minutes,
                    timeframe, indicators_data, market_conditions
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    signal_id,
                    entry_time,
                    signal_data.get('asset'),
                    signal_data.get('signal_type'),
                    signal_data.get('score'),
                    signal_data.get('entry_price'),
                    entry_time,
                    expiry_time,
                    expiry_minutes,
                    signal_data.get('timeframe', '5m'),
                    json.dumps(signal_data.get('indicators', {})),
                    json.dumps(signal_data.get('market_conditions', {}))
                ))
                
                conn.commit()
                print(f"📊 Traditional signal tracked: {signal_id} - {signal_data.get('asset')} {signal_data.get('signal_type')}")
                return True
                
        except Exception as e:
            print(f"❌ Error tracking traditional signal: {e}")
            return False
    
    def start_tracking(self):
        """Start the tracking system."""
        if not self.tracking_active:
            self.tracking_active = True
            self.tracking_thread = threading.Thread(target=self._tracking_loop, daemon=True)
            self.tracking_thread.start()
            print("🔄 Traditional signal tracking started")
    
    def stop_tracking(self):
        """Stop the tracking system."""
        self.tracking_active = False
        if self.tracking_thread:
            self.tracking_thread.join(timeout=5)
        print("⏹️ Traditional signal tracking stopped")
    
    def _tracking_loop(self):
        """Main tracking loop for evaluating expired signals."""
        while self.tracking_active:
            try:
                self._evaluate_expired_signals()
                self._analyze_patterns()
                self._generate_ai_insights()
                time.sleep(self.check_interval)
            except Exception as e:
                print(f"❌ Error in traditional signal tracking loop: {e}")
                time.sleep(self.check_interval)
    
    def _evaluate_expired_signals(self):
        """Evaluate expired traditional signals."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get expired signals that haven't been evaluated
                cursor.execute("""
                SELECT ts.signal_id, ts.asset, ts.signal_type, ts.entry_price,
                       ts.expiry_time, ts.score, ts.indicators_data
                FROM traditional_signals ts
                LEFT JOIN traditional_signal_results tsr ON ts.signal_id = tsr.signal_id
                WHERE ts.expiry_time <= ? AND tsr.signal_id IS NULL
                """, (datetime.now(),))
                
                expired_signals = cursor.fetchall()
                
                for signal in expired_signals:
                    self._evaluate_signal(signal)
                    
        except Exception as e:
            print(f"❌ Error evaluating expired traditional signals: {e}")
    
    def _evaluate_signal(self, signal: Tuple):
        """Evaluate a single traditional signal."""
        try:
            signal_id, asset, signal_type, entry_price, expiry_time, score, indicators_data = signal
            
            # Get current price
            exit_price = self._get_current_price(asset)
            if exit_price is None:
                return
            
            # Calculate result based on signal type
            result = self._determine_result(signal_type, entry_price, exit_price, score)
            profit_loss = self._calculate_profit_loss(signal_type, entry_price, exit_price)
            price_movement = ((exit_price - entry_price) / entry_price) * 100
            
            # Store result
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                INSERT INTO traditional_signal_results (
                    signal_id, exit_price, result, profit_loss,
                    evaluation_method, evaluated_at, price_movement
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    signal_id, exit_price, result, profit_loss,
                    "AUTO", datetime.now(), price_movement
                ))
                
                conn.commit()
                
            print(f"✅ Traditional signal {signal_id} evaluated: {result} ({profit_loss:+.2f}%)")
            
        except Exception as e:
            print(f"❌ Error evaluating traditional signal: {e}")
    
    def _get_current_price(self, asset: str) -> Optional[float]:
        """Get current price for an asset."""
        try:
            # Use TradingView API or fallback to demo price
            # This is a simplified implementation
            import random
            base_prices = {
                'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 150.25,
                'AUDUSD': 0.6650, 'USDCAD': 1.3580, 'USDCHF': 0.8850
            }
            base_price = base_prices.get(asset, 1.0000)
            # Add some random movement
            movement = random.uniform(-0.002, 0.002)
            return base_price * (1 + movement)
            
        except Exception as e:
            print(f"❌ Error getting price for {asset}: {e}")
            return None
    
    def _determine_result(self, signal_type: str, entry_price: float, exit_price: float, score: float) -> str:
        """Determine if signal was WIN, LOSS, or TIE."""
        price_change = exit_price - entry_price
        
        if "Buy" in signal_type:
            if price_change > 0:
                return "WIN"
            elif price_change < 0:
                return "LOSS"
            else:
                return "TIE"
        elif "Sell" in signal_type:
            if price_change < 0:
                return "WIN"
            elif price_change > 0:
                return "LOSS"
            else:
                return "TIE"
        else:
            return "TIE"
    
    def _calculate_profit_loss(self, signal_type: str, entry_price: float, exit_price: float) -> float:
        """Calculate profit/loss percentage."""
        price_change_pct = ((exit_price - entry_price) / entry_price) * 100
        
        if "Buy" in signal_type:
            return price_change_pct
        elif "Sell" in signal_type:
            return -price_change_pct
        else:
            return 0.0
    
    def get_performance_stats(self, days: int = 7) -> Dict[str, Any]:
        """Get performance statistics for traditional signals."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                since_date = datetime.now() - timedelta(days=days)
                
                cursor.execute("""
                SELECT 
                    COUNT(*) as total_signals,
                    SUM(CASE WHEN tsr.result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    SUM(CASE WHEN tsr.result = 'LOSS' THEN 1 ELSE 0 END) as losses,
                    AVG(tsr.profit_loss) as avg_profit_loss,
                    ts.signal_type,
                    ts.asset
                FROM traditional_signals ts
                JOIN traditional_signal_results tsr ON ts.signal_id = tsr.signal_id
                WHERE ts.timestamp >= ?
                GROUP BY ts.signal_type, ts.asset
                """, (since_date,))
                
                results = cursor.fetchall()
                
                stats = {
                    'overall': {'total': 0, 'wins': 0, 'losses': 0, 'win_rate': 0},
                    'by_signal_type': {},
                    'by_asset': {}
                }
                
                total_signals = 0
                total_wins = 0
                
                for row in results:
                    total, wins, losses, avg_pl, signal_type, asset = row
                    total_signals += total
                    total_wins += wins
                    
                    win_rate = (wins / total * 100) if total > 0 else 0
                    
                    if signal_type not in stats['by_signal_type']:
                        stats['by_signal_type'][signal_type] = {
                            'total': 0, 'wins': 0, 'losses': 0, 'win_rate': 0
                        }
                    
                    stats['by_signal_type'][signal_type]['total'] += total
                    stats['by_signal_type'][signal_type]['wins'] += wins
                    stats['by_signal_type'][signal_type]['losses'] += losses
                    stats['by_signal_type'][signal_type]['win_rate'] = win_rate
                
                stats['overall'] = {
                    'total': total_signals,
                    'wins': total_wins,
                    'losses': total_signals - total_wins,
                    'win_rate': (total_wins / total_signals * 100) if total_signals > 0 else 0
                }
                
                return stats
                
        except Exception as e:
            print(f"❌ Error getting traditional signal performance stats: {e}")
            return {'overall': {'total': 0, 'wins': 0, 'losses': 0, 'win_rate': 0}}

    def _analyze_patterns(self):
        """Analyze signal patterns to identify winning strategies."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Analyze patterns by signal type and asset
                cursor.execute("""
                SELECT
                    ts.asset,
                    ts.signal_type,
                    COUNT(*) as total_signals,
                    SUM(CASE WHEN tsr.result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    AVG(ts.score) as avg_score,
                    AVG(tsr.profit_loss) as avg_profit_loss
                FROM traditional_signals ts
                JOIN traditional_signal_results tsr ON ts.signal_id = tsr.signal_id
                WHERE ts.timestamp >= datetime('now', '-7 days')
                GROUP BY ts.asset, ts.signal_type
                HAVING COUNT(*) >= 5
                """)

                patterns = cursor.fetchall()

                for pattern in patterns:
                    asset, signal_type, total, wins, avg_score, avg_pl = pattern
                    win_rate = (wins / total * 100) if total > 0 else 0

                    # Update or insert pattern analysis
                    cursor.execute("""
                    INSERT OR REPLACE INTO signal_patterns (
                        pattern_name, asset, signal_type, win_rate,
                        total_signals, avg_score, last_updated
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        f"{asset}_{signal_type}",
                        asset, signal_type, win_rate,
                        total, avg_score, datetime.now()
                    ))

                conn.commit()

        except Exception as e:
            print(f"❌ Error analyzing traditional signal patterns: {e}")

    def _generate_ai_insights(self):
        """Generate AI insights for signal optimization."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Find high-performing patterns
                cursor.execute("""
                SELECT asset, signal_type, win_rate, total_signals, avg_score
                FROM signal_patterns
                WHERE win_rate >= 70 AND total_signals >= 10
                ORDER BY win_rate DESC
                """)

                high_performers = cursor.fetchall()

                for pattern in high_performers:
                    asset, signal_type, win_rate, total, avg_score = pattern

                    insight_data = {
                        'pattern_type': 'HIGH_PERFORMANCE',
                        'asset': asset,
                        'signal_type': signal_type,
                        'win_rate': win_rate,
                        'recommended_min_score': avg_score * 0.8,
                        'recommendation': f"Prioritize {signal_type} signals for {asset} with score >= {avg_score * 0.8:.1f}"
                    }

                    cursor.execute("""
                    INSERT OR IGNORE INTO ai_insights (
                        insight_type, asset, signal_type, insight_data, confidence
                    ) VALUES (?, ?, ?, ?, ?)
                    """, (
                        'WIN_PATTERN', asset, signal_type,
                        json.dumps(insight_data), win_rate
                    ))

                # Find poor-performing patterns
                cursor.execute("""
                SELECT asset, signal_type, win_rate, total_signals
                FROM signal_patterns
                WHERE win_rate < 40 AND total_signals >= 10
                """)

                poor_performers = cursor.fetchall()

                for pattern in poor_performers:
                    asset, signal_type, win_rate, total = pattern

                    insight_data = {
                        'pattern_type': 'POOR_PERFORMANCE',
                        'asset': asset,
                        'signal_type': signal_type,
                        'win_rate': win_rate,
                        'recommendation': f"Avoid or reduce {signal_type} signals for {asset}"
                    }

                    cursor.execute("""
                    INSERT OR IGNORE INTO ai_insights (
                        insight_type, asset, signal_type, insight_data, confidence
                    ) VALUES (?, ?, ?, ?, ?)
                    """, (
                        'LOSS_PATTERN', asset, signal_type,
                        json.dumps(insight_data), 100 - win_rate
                    ))

                conn.commit()

        except Exception as e:
            print(f"❌ Error generating AI insights: {e}")

    def get_ai_recommendations(self, asset: str = None) -> List[Dict[str, Any]]:
        """Get AI recommendations for signal optimization."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                query = """
                SELECT insight_type, asset, signal_type, insight_data, confidence
                FROM ai_insights
                WHERE created_at >= datetime('now', '-7 days')
                """
                params = []

                if asset:
                    query += " AND asset = ?"
                    params.append(asset)

                query += " ORDER BY confidence DESC LIMIT 10"

                cursor.execute(query, params)
                results = cursor.fetchall()

                recommendations = []
                for row in results:
                    insight_type, asset, signal_type, insight_data, confidence = row

                    recommendation = {
                        'type': insight_type,
                        'asset': asset,
                        'signal_type': signal_type,
                        'data': json.loads(insight_data),
                        'confidence': confidence
                    }
                    recommendations.append(recommendation)

                return recommendations

        except Exception as e:
            print(f"❌ Error getting AI recommendations: {e}")
            return []

    def get_recent_signals(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent traditional signals with results."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute("""
                SELECT
                    ts.signal_id, ts.timestamp, ts.asset, ts.signal_type,
                    ts.score, ts.entry_price, ts.entry_time, ts.expiry_time,
                    tsr.exit_price, tsr.result, tsr.profit_loss, tsr.evaluated_at
                FROM traditional_signals ts
                LEFT JOIN traditional_signal_results tsr ON ts.signal_id = tsr.signal_id
                ORDER BY ts.timestamp DESC
                LIMIT ?
                """, (limit,))

                columns = [desc[0] for desc in cursor.description]
                results = []

                for row in cursor.fetchall():
                    signal_dict = dict(zip(columns, row))
                    results.append(signal_dict)

                return results

        except Exception as e:
            print(f"❌ Error getting recent traditional signals: {e}")
            return []
